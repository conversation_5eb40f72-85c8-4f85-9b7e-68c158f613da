# Automatic Context Management Improvements for <PERSON><PERSON>

## Overview

This enhancement adds intelligent automatic context management to aider, allowing it to automatically add and remove files from the chat context based on relevance analysis, without removing the manual `/add` and `/drop` commands.

## Key Features

### 1. **Intelligent File Relevance Scoring**

The system scores files based on multiple factors:

- **Direct mentions** in user messages (10.0 points)
- **Symbol/identifier mentions** (8.0 points) 
- **Import/dependency relationships** (up to 10.0 points)
- **Recent edit history** (5.0 points)
- **File size penalty** (up to -2.0 points for large files)
- **Test file relevance** (6.0 points when working on implementation)

### 2. **Automatic Context Updates**

- **Auto-add**: Automatically adds relevant files when they exceed the relevance threshold
- **Auto-remove**: Optionally removes low-relevance files when in aggressive mode
- **Smart limits**: Respects maximum context file limits to prevent token overflow
- **Conservative by default**: Only adds files unless aggressive mode is enabled

### 3. **New Commands**

#### `/auto-context` - Control automatic context management
```bash
/auto-context status          # Show current settings
/auto-context on/off          # Enable/disable auto-context
/auto-context aggressive      # Enable auto-removal of files
/auto-context conservative    # Disable auto-removal (default)
/auto-context max 20          # Set max context files
/auto-context threshold 2.5   # Set relevance threshold
```

#### `/suggest-context` - Preview context suggestions
```bash
/suggest-context "I want to add a new API endpoint"
# Shows suggested files to add/remove with reasoning
```

### 4. **Configuration Options**

New command-line arguments:
```bash
--auto-context / --no-auto-context           # Enable/disable (default: True)
--auto-context-max-files 20                  # Max files in context (default: 15)
--auto-context-threshold 2.5                 # Relevance threshold (default: 3.0)
--auto-context-aggressive                     # Enable auto-removal (default: False)
```

## How It Works

### File Selection Algorithm

1. **Context Analysis**: Extracts mentioned files, identifiers, and classifies request type
2. **Relevance Scoring**: Scores all available files using multiple factors
3. **Smart Selection**: Adds highest-scoring files up to the limit
4. **Optional Removal**: In aggressive mode, removes lowest-scoring files

### Integration Points

- **Message Processing**: Runs before each user message is processed
- **Non-intrusive**: Preserves all existing manual commands
- **Configurable**: Can be disabled or tuned per user preference
- **Learning**: Tracks usage patterns for future improvements

## Example Usage

### Scenario 1: Adding a New Feature
```
User: "I want to add a new user authentication endpoint"

Auto-context analysis:
✓ Detected files: auth.py, user.py, endpoints.py
✓ Auto-added: auth.py (mentioned symbols: authenticate, login)
✓ Auto-added: endpoints.py (dependency relationship)
✓ Auto-added: test_auth.py (test file relevance)

Auto-context: +3 -0 files
```

### Scenario 2: Bug Fix
```
User: "Fix the bug in the payment processing where transactions fail"

Auto-context analysis:
✓ Detected files: payment.py, transaction.py
✓ Auto-added: payment.py (direct mention)
✓ Auto-added: transaction.py (mentioned symbols: fail, process)
✓ Auto-added: models/payment.py (dependency relationship)

Auto-context: +3 -0 files
```

### Scenario 3: Aggressive Mode
```
User: "Update the logging configuration"

Auto-context analysis (aggressive mode):
✓ Auto-added: logging.py (direct mention)
✓ Auto-added: config.py (dependency relationship)
✗ Auto-removed: old_feature.py (low relevance: 1.2)
✗ Auto-removed: unused_utils.py (low relevance: 0.8)

Auto-context: +2 -2 files
```

## Benefits

1. **Reduced Manual Work**: Users don't need to manually add/remove files as often
2. **Better Context**: More relevant files are automatically included
3. **Token Efficiency**: Prevents context window overflow with smart limits
4. **Maintains Control**: Manual commands still work for fine-tuning
5. **Learning System**: Improves over time based on usage patterns

## Implementation Details

### Core Classes

- **`AutoContextManager`**: Main orchestrator for context decisions
- **`FileRelevanceScorer`**: Calculates relevance scores for files
- **Integration in `BaseCoder`**: Hooks into message processing pipeline

### Safety Features

- **Conservative defaults**: Won't remove files unless explicitly enabled
- **Error handling**: Graceful degradation if auto-context fails
- **User feedback**: Clear messages about what was added/removed
- **Override capability**: Manual commands always take precedence

## Future Enhancements

1. **Machine Learning**: Train models on user behavior patterns
2. **Project-specific rules**: Learn project-specific file relationships
3. **Semantic analysis**: Better understanding of code relationships
4. **Performance optimization**: Faster relevance calculations for large repos
5. **Integration with IDE**: Sync with editor's open files and recent changes

This enhancement makes aider more intelligent and user-friendly while preserving full user control over the context management process.
