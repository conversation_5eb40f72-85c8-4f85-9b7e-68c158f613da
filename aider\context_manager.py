# Automatic Context Management for Aider
import os
import time
from collections import defaultdict, deque
from pathlib import Path
from typing import Set, Dict, List, Tuple, Optional
import re


class FileRelevanceScorer:
    """Scores file relevance based on various factors."""
    
    def __init__(self, coder):
        self.coder = coder
        self.repo_map = coder.repo_map
        
    def score_file_relevance(self, file_path: str, context: Dict) -> float:
        """Score how relevant a file is to the current context."""
        score = 0.0
        
        # Factor 1: Direct mentions in user message
        if file_path in context.get('mentioned_files', set()):
            score += 10.0
            
        # Factor 2: Symbol/identifier mentions
        if self._has_mentioned_symbols(file_path, context.get('mentioned_idents', set())):
            score += 8.0
            
        # Factor 3: Import/dependency relationships
        dependency_score = self._calculate_dependency_score(file_path, context.get('current_files', set()))
        score += dependency_score
        
        # Factor 4: Recent edit history
        if self._recently_edited(file_path):
            score += 5.0
            
        # Factor 5: File size penalty (prefer smaller, focused files)
        size_penalty = self._calculate_size_penalty(file_path)
        score -= size_penalty
        
        # Factor 6: Test file bonus if working on implementation
        if self._is_test_file_relevant(file_path, context):
            score += 6.0
            
        return max(0.0, score)
    
    def _has_mentioned_symbols(self, file_path: str, mentioned_idents: Set[str]) -> bool:
        """Check if file contains any mentioned symbols."""
        if not mentioned_idents or not self.repo_map:
            return False
            
        # Use repo map's tag information to check for symbol matches
        try:
            tags = self.repo_map.get_tags_for_file(file_path)
            file_symbols = {tag.name for tag in tags if tag}
            return bool(mentioned_idents & file_symbols)
        except:
            return False
    
    def _calculate_dependency_score(self, file_path: str, current_files: Set[str]) -> float:
        """Calculate score based on import/dependency relationships."""
        if not current_files:
            return 0.0
            
        score = 0.0
        try:
            # Check if this file imports any current files
            for current_file in current_files:
                if self._files_have_dependency(file_path, current_file):
                    score += 3.0
                elif self._files_have_dependency(current_file, file_path):
                    score += 2.0
        except:
            pass
            
        return min(score, 10.0)  # Cap the dependency score
    
    def _files_have_dependency(self, file1: str, file2: str) -> bool:
        """Check if file1 depends on file2."""
        # This is a simplified check - could be enhanced with AST parsing
        try:
            content = self.coder.io.read_text(file1)
            if not content:
                return False
                
            file2_name = Path(file2).stem
            # Look for import statements or references
            import_patterns = [
                rf'import.*{re.escape(file2_name)}',
                rf'from.*{re.escape(file2_name)}',
                rf'require.*{re.escape(file2_name)}',
                rf'#include.*{re.escape(file2_name)}'
            ]
            
            for pattern in import_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return True
        except:
            pass
        return False
    
    def _recently_edited(self, file_path: str) -> bool:
        """Check if file was recently edited."""
        try:
            mtime = os.path.getmtime(file_path)
            return (time.time() - mtime) < 3600  # Within last hour
        except:
            return False
    
    def _calculate_size_penalty(self, file_path: str) -> float:
        """Calculate penalty based on file size."""
        try:
            size = os.path.getsize(file_path)
            # Penalty increases with size, but caps at 2.0
            return min(size / 50000, 2.0)  # 50KB = 1.0 penalty
        except:
            return 0.0
    
    def _is_test_file_relevant(self, file_path: str, context: Dict) -> bool:
        """Check if this test file is relevant to current implementation files."""
        if not self._is_test_file(file_path):
            return False
            
        current_files = context.get('current_files', set())
        for current_file in current_files:
            if self._test_matches_implementation(file_path, current_file):
                return True
        return False
    
    def _is_test_file(self, file_path: str) -> bool:
        """Check if file is a test file."""
        path = Path(file_path)
        return (
            'test' in path.name.lower() or
            path.name.startswith('test_') or
            path.name.endswith('_test.py') or
            'tests' in str(path).lower()
        )
    
    def _test_matches_implementation(self, test_file: str, impl_file: str) -> bool:
        """Check if test file corresponds to implementation file."""
        test_stem = Path(test_file).stem.lower()
        impl_stem = Path(impl_file).stem.lower()
        
        # Remove test prefixes/suffixes
        test_clean = test_stem.replace('test_', '').replace('_test', '')
        
        return test_clean == impl_stem or impl_stem in test_clean


class AutoContextManager:
    """Automatically manages file context based on user requests and code analysis."""
    
    def __init__(self, coder, max_context_files=15, relevance_threshold=3.0):
        self.coder = coder
        self.scorer = FileRelevanceScorer(coder)
        self.max_context_files = max_context_files
        self.relevance_threshold = relevance_threshold
        
        # Track context history for learning
        self.context_history = deque(maxlen=50)
        self.file_usage_stats = defaultdict(int)
        
        # Settings
        self.auto_add_enabled = True
        self.auto_remove_enabled = True
        self.aggressive_mode = False  # More aggressive file addition/removal
        
    def analyze_and_update_context(self, user_message: str) -> Dict[str, List[str]]:
        """Analyze user message and automatically update context."""
        if not self.auto_add_enabled and not self.auto_remove_enabled:
            return {'added': [], 'removed': []}
            
        # Extract context from user message
        context = self._extract_context_from_message(user_message)
        
        # Get current context state
        current_files = set(self.coder.get_inchat_relative_files())
        
        # Find files to add
        files_to_add = []
        if self.auto_add_enabled:
            files_to_add = self._find_files_to_add(context, current_files)
            
        # Find files to remove
        files_to_remove = []
        if self.auto_remove_enabled:
            files_to_remove = self._find_files_to_remove(context, current_files)
            
        # Apply changes
        added_files = self._add_files(files_to_add)
        removed_files = self._remove_files(files_to_remove)
        
        # Update usage statistics
        self._update_usage_stats(added_files, removed_files, context)
        
        return {
            'added': added_files,
            'removed': removed_files,
            'context': context
        }
    
    def _extract_context_from_message(self, message: str) -> Dict:
        """Extract relevant context information from user message."""
        context = {
            'mentioned_files': self.coder.get_file_mentions(message),
            'mentioned_idents': self.coder.get_ident_mentions(message),
            'current_files': set(self.coder.get_inchat_relative_files()),
            'message_type': self._classify_message_type(message),
            'complexity': self._estimate_complexity(message)
        }
        return context
    
    def _classify_message_type(self, message: str) -> str:
        """Classify the type of user request."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['test', 'testing', 'unit test', 'pytest']):
            return 'testing'
        elif any(word in message_lower for word in ['refactor', 'restructure', 'reorganize']):
            return 'refactoring'
        elif any(word in message_lower for word in ['bug', 'fix', 'error', 'issue']):
            return 'bugfix'
        elif any(word in message_lower for word in ['add', 'create', 'new', 'implement']):
            return 'feature'
        elif any(word in message_lower for word in ['update', 'modify', 'change']):
            return 'modification'
        else:
            return 'general'
    
    def _estimate_complexity(self, message: str) -> str:
        """Estimate the complexity of the request."""
        word_count = len(message.split())
        
        if word_count < 10:
            return 'simple'
        elif word_count < 30:
            return 'medium'
        else:
            return 'complex'
    
    def _find_files_to_add(self, context: Dict, current_files: Set[str]) -> List[str]:
        """Find files that should be added to context."""
        candidates = []
        
        # Get all available files
        all_files = set(self.coder.get_all_relative_files()) - current_files
        
        # Score each candidate file
        scored_files = []
        for file_path in all_files:
            if self._should_consider_file(file_path):
                score = self.scorer.score_file_relevance(file_path, context)
                if score >= self.relevance_threshold:
                    scored_files.append((score, file_path))
        
        # Sort by score and limit by max_context_files
        scored_files.sort(reverse=True)
        available_slots = self.max_context_files - len(current_files)
        
        candidates = [file_path for score, file_path in scored_files[:available_slots]]
        
        return candidates
    
    def _find_files_to_remove(self, context: Dict, current_files: Set[str]) -> List[str]:
        """Find files that should be removed from context."""
        if not self.aggressive_mode:
            return []  # Conservative approach - don't auto-remove unless aggressive
            
        files_to_remove = []
        
        # Only remove if we're at capacity and have better candidates
        if len(current_files) >= self.max_context_files:
            # Score current files for removal
            removal_candidates = []
            for file_path in current_files:
                score = self.scorer.score_file_relevance(file_path, context)
                removal_candidates.append((score, file_path))
            
            # Remove lowest scoring files
            removal_candidates.sort()
            threshold = self.relevance_threshold * 0.5  # Lower threshold for removal
            
            for score, file_path in removal_candidates:
                if score < threshold:
                    files_to_remove.append(file_path)
        
        return files_to_remove
    
    def _should_consider_file(self, file_path: str) -> bool:
        """Check if file should be considered for addition."""
        # Skip certain file types
        skip_extensions = {'.pyc', '.pyo', '.pyd', '.so', '.dll', '.exe', '.bin'}
        if Path(file_path).suffix.lower() in skip_extensions:
            return False
            
        # Skip very large files
        try:
            if os.path.getsize(file_path) > 1024 * 1024:  # 1MB
                return False
        except:
            return False
            
        return True
    
    def _add_files(self, files_to_add: List[str]) -> List[str]:
        """Add files to context."""
        added = []
        for file_path in files_to_add:
            try:
                self.coder.add_rel_fname(file_path)
                added.append(file_path)
                if self.coder.io:
                    self.coder.io.tool_output(f"Auto-added {file_path} to context")
            except Exception as e:
                if self.coder.io:
                    self.coder.io.tool_warning(f"Failed to auto-add {file_path}: {e}")
        return added
    
    def _remove_files(self, files_to_remove: List[str]) -> List[str]:
        """Remove files from context."""
        removed = []
        for file_path in files_to_remove:
            try:
                if self.coder.drop_rel_fname(file_path):
                    removed.append(file_path)
                    if self.coder.io:
                        self.coder.io.tool_output(f"Auto-removed {file_path} from context")
            except Exception as e:
                if self.coder.io:
                    self.coder.io.tool_warning(f"Failed to auto-remove {file_path}: {e}")
        return removed
    
    def _update_usage_stats(self, added_files: List[str], removed_files: List[str], context: Dict):
        """Update usage statistics for learning."""
        for file_path in added_files:
            self.file_usage_stats[file_path] += 1
            
        # Store context decision for future learning
        self.context_history.append({
            'timestamp': time.time(),
            'added': added_files,
            'removed': removed_files,
            'context': context
        })
    
    def get_context_suggestions(self, user_message: str) -> Dict[str, List[str]]:
        """Get context suggestions without applying them."""
        context = self._extract_context_from_message(user_message)
        current_files = set(self.coder.get_inchat_relative_files())
        
        suggested_adds = self._find_files_to_add(context, current_files)
        suggested_removes = self._find_files_to_remove(context, current_files)
        
        return {
            'suggested_adds': suggested_adds,
            'suggested_removes': suggested_removes,
            'reasoning': self._explain_suggestions(suggested_adds, suggested_removes, context)
        }
    
    def _explain_suggestions(self, adds: List[str], removes: List[str], context: Dict) -> Dict:
        """Provide reasoning for suggestions."""
        reasoning = {
            'adds': {},
            'removes': {}
        }
        
        for file_path in adds:
            score = self.scorer.score_file_relevance(file_path, context)
            reasoning['adds'][file_path] = f"Relevance score: {score:.1f}"
            
        for file_path in removes:
            score = self.scorer.score_file_relevance(file_path, context)
            reasoning['removes'][file_path] = f"Low relevance score: {score:.1f}"
            
        return reasoning
